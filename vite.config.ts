import { defineConfig } from 'vite'
import uni from '@dcloudio/vite-plugin-uni'
import { resolve } from 'path'

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [
    uni()
  ],

  // 路径别名配置
  resolve: {
    alias: [
      {
        find: '@',
        replacement: resolve(__dirname, 'src')
      },
      {
        find: '@utils',
        replacement: resolve(__dirname, 'src/utils')
      },
      {
        find: '@api',
        replacement: resolve(__dirname, 'src/api')
      },
      {
        find: '@store',
        replacement: resolve(__dirname, 'src/store')
      },
      {
        find: '@types',
        replacement: resolve(__dirname, 'src/types')
      },
      {
        find: '@styles',
        replacement: resolve(__dirname, 'src/styles')
      },
      {
        find: '@static',
        replacement: resolve(__dirname, 'src/static')
      },

    ]
  },

  // CSS预处理器配置
  css: {
    preprocessorOptions: {
      scss: {
        // SCSS 预处理器配置
        charset: false
      }
    }
  },

  // 环境变量配置
  define: {
    __PLATFORM__: JSON.stringify(process.env.UNI_PLATFORM),
    'process.env.NODE_ENV': JSON.stringify(process.env.NODE_ENV || 'development')
  },

  // 开发服务器配置
  server: {
    port: 3000,
    host: '0.0.0.0', // 支持局域网访问
    open: true,
    cors: true
  },

  // 构建配置
  build: {
    target: 'es2015',
    cssTarget: 'chrome61',
    rollupOptions: {
      external: [],
      output: {
        globals: {}
      }
    }
  },

  // 优化配置
  optimizeDeps: {
    include: []
  }
})
