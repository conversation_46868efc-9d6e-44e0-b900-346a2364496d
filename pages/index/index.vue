<template>
	<view class="content">
		<view class="text-area">
			<text class="title">{{title}}</text>
		</view>

		<!-- 使用 uni-app 原生组件 -->
		<view class="uni-demo">
			<view class="card">
				<view class="card-title">基础组件</view>
				<view class="cell-group">
					<view class="cell">
						<text class="cell-title">单元格</text>
						<text class="cell-value">内容</text>
					</view>
					<view class="cell">
						<text class="cell-title">
							uni-app
						</text>
					</view>
					<view class="cell">
						<text class="cell-title">带图标</text>
						<text class="cell-value">内容</text>
					</view>
				</view>
			</view>

			<view class="button-group">
				<button type="primary" @click="showToast" class="uni-button">显示 Toast</button>
				<button type="default" @click="showDialog" class="uni-button success">显示 Dialog</button>
			</view>

			<view class="input-group">
				<view class="input-label">输入框</view>
				<input
					v-model="inputValue"
					placeholder="请输入内容"
					class="uni-input"
				/>
			</view>
		</view>
	</view>
	
	<!-- 自定义TabBar -->
	<custom-tabbar :current="0"></custom-tabbar>
</template>

<script>
	import CustomTabbar from '../../components/custom-tabbar/custom-tabbar.vue'
	
	export default {
		components: {
			CustomTabbar
		},
		data() {
			return {
				title: 'uni-app 原生组件示例',
				inputValue: ''
			}
		},
		onLoad() {

		},
		
		methods: {
			showToast() {
				// 使用 UniApp 的 Toast（推荐在 UniApp 中使用）
				uni.showToast({
					title: '这是 UniApp 的 Toast！',
					icon: 'success'
				})
			},
			showDialog() {
				// 使用 UniApp 的 Modal（推荐在 UniApp 中使用）
				uni.showModal({
					title: '提示',
					content: '这是 UniApp 的 Modal 组件',
					success: (res) => {
						if (res.confirm) {
							console.log('用户点击确定')
						} else if (res.cancel) {
							console.log('用户点击取消')
						}
					}
				})
			}
		}
	}
</script>

<style>
	.content {
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: flex-start;
		padding: 40rpx 40rpx 160rpx 40rpx;
		min-height: 100vh;
	}

	.logo {
		height: 150rpx;
		width: 150rpx;
		margin-bottom: 30rpx;
	}

	.text-area {
		display: flex;
		justify-content: center;
		margin-bottom: 40rpx;
	}

	.title {
		font-size: 36rpx;
		color: #333;
		font-weight: bold;
	}

	.uni-demo {
		width: 100%;
		max-width: 600rpx;
	}

	.card {
		background: #fff;
		border-radius: 16rpx;
		margin-bottom: 30rpx;
		overflow: hidden;
		box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.1);
	}

	.card-title {
		padding: 30rpx;
		font-size: 32rpx;
		font-weight: bold;
		color: #323233;
		border-bottom: 1rpx solid #ebedf0;
	}

	.cell-group {
		background: #fff;
	}

	.cell {
		display: flex;
		align-items: center;
		padding: 24rpx 30rpx;
		border-bottom: 1rpx solid #ebedf0;
		position: relative;
	}

	.cell:last-child {
		border-bottom: none;
	}

	.cell-title {
		flex: 1;
		font-size: 28rpx;
		color: #323233;
	}

	.cell-value {
		font-size: 28rpx;
		color: #969799;
		margin-right: 20rpx;
	}

	.cell-icon {
		font-size: 32rpx;
	}

	.button-group {
		margin: 30rpx 0;
		display: flex;
		gap: 20rpx;
	}

	.uni-button {
		flex: 1;
		height: 88rpx;
		line-height: 88rpx;
		border-radius: 12rpx;
		font-size: 28rpx;
		border: none;
	}

	.uni-button.success {
		background-color: #07c160;
		color: white;
	}

	.input-group {
		background: #fff;
		border-radius: 16rpx;
		padding: 30rpx;
		box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.1);
	}

	.input-label {
		font-size: 28rpx;
		color: #323233;
		margin-bottom: 20rpx;
	}

	.uni-input {
		width: 100%;
		height: 88rpx;
		padding: 0 24rpx;
		border: 1rpx solid #ebedf0;
		border-radius: 12rpx;
		font-size: 28rpx;
		background: #f7f8fa;
	}
</style>
