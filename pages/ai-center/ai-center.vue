<template>
	<view class="ai-center-container">
		<view class="header">
			<view class="ai-avatar">
				<image src="/static/icons/ai-avatar.png" class="avatar-img"></image>
			</view>
			<text class="title">AI百应</text>
			<text class="subtitle">您的智能助手</text>
		</view>
		
		<view class="chat-container">
			<scroll-view 
				class="message-list" 
				:scroll-y="true" 
				:scroll-top="scrollTop"
			>
				<view 
					class="message-item" 
					v-for="(message, index) in messages" 
					:key="index"
					:class="message.type"
				>
					<view class="message-avatar" v-if="message.type === 'ai'">
						<image src="/static/icons/ai-avatar.png" class="avatar"></image>
					</view>
					<view class="message-content">
						<text class="message-text">{{ message.content }}</text>
						<text class="message-time">{{ message.time }}</text>
					</view>
					<view class="message-avatar" v-if="message.type === 'user'">
						<image src="/static/icons/user-avatar.png" class="avatar"></image>
					</view>
				</view>
			</scroll-view>
		</view>
		
		<view class="input-container">
			<view class="quick-actions">
				<view 
					class="action-item" 
					v-for="(action, index) in quickActions" 
					:key="index"
					@click="sendQuickMessage(action.text)"
				>
					<text class="action-text">{{ action.text }}</text>
				</view>
			</view>
			
			<view class="input-bar">
				<input 
					v-model="inputMessage"
					placeholder="请输入您的问题..."
					class="message-input"
					@confirm="sendMessage"
				/>
				<button 
					class="send-btn" 
					@click="sendMessage"
					:disabled="!inputMessage.trim()"
				>
					发送
				</button>
			</view>
		</view>
	</view>
	
	<!-- 自定义TabBar -->
	<custom-tabbar :current="2"></custom-tabbar>
</template>

<script>
import CustomTabbar from '../../components/custom-tabbar/custom-tabbar.vue'

export default {
	components: {
		CustomTabbar
	},
	data() {
		return {
			inputMessage: '',
			scrollTop: 0,
			messages: [
				{
					type: 'ai',
					content: '您好！我是AI百应助手，有什么可以帮助您的吗？',
					time: this.getCurrentTime()
				}
			],
			quickActions: [
				{ text: '今日推荐' },
				{ text: '优惠活动' },
				{ text: '产品咨询' },
				{ text: '使用帮助' }
			]
		}
	},
	onLoad() {
		console.log('AI百应页面加载')
	},
	methods: {
		sendMessage() {
			if (!this.inputMessage.trim()) return;
			
			// 添加用户消息
			this.messages.push({
				type: 'user',
				content: this.inputMessage,
				time: this.getCurrentTime()
			});
			
			// 模拟AI回复
			setTimeout(() => {
				this.messages.push({
					type: 'ai',
					content: this.getAIResponse(this.inputMessage),
					time: this.getCurrentTime()
				});
				this.scrollToBottom();
			}, 1000);
			
			this.inputMessage = '';
			this.scrollToBottom();
		},
		
		sendQuickMessage(text) {
			this.inputMessage = text;
			this.sendMessage();
		},
		
		getAIResponse(message) {
			const responses = [
				'感谢您的咨询，我正在为您查找相关信息...',
				'这是一个很好的问题，让我为您详细解答。',
				'根据您的需求，我推荐以下几个选项...',
				'我理解您的关注，这里有一些建议供您参考。'
			];
			return responses[Math.floor(Math.random() * responses.length)];
		},
		
		getCurrentTime() {
			const now = new Date();
			return `${now.getHours().toString().padStart(2, '0')}:${now.getMinutes().toString().padStart(2, '0')}`;
		},
		
		scrollToBottom() {
			this.$nextTick(() => {
				this.scrollTop = 999999;
			});
		}
	}
}
</script>

<style scoped>
.ai-center-container {
	display: flex;
	flex-direction: column;
	height: 100vh;
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	padding-bottom: 170rpx;
}

.header {
	text-align: center;
	padding: 40rpx 20rpx 20rpx;
	color: white;
}

.ai-avatar {
	margin-bottom: 20rpx;
}

.avatar-img {
	width: 120rpx;
	height: 120rpx;
	border-radius: 60rpx;
	border: 4rpx solid rgba(255, 255, 255, 0.3);
}

.title {
	font-size: 40rpx;
	font-weight: bold;
	display: block;
	margin-bottom: 10rpx;
}

.subtitle {
	font-size: 28rpx;
	opacity: 0.8;
}

.chat-container {
	flex: 1;
	background-color: #f5f5f5;
	border-top-left-radius: 30rpx;
	border-top-right-radius: 30rpx;
	overflow: hidden;
}

.message-list {
	height: 100%;
	padding: 20rpx;
}

.message-item {
	display: flex;
	margin-bottom: 30rpx;
	align-items: flex-end;
}

.message-item.user {
	flex-direction: row-reverse;
}

.message-avatar {
	margin: 0 20rpx;
}

.avatar {
	width: 60rpx;
	height: 60rpx;
	border-radius: 30rpx;
}

.message-content {
	max-width: 60%;
	background-color: #fff;
	padding: 20rpx;
	border-radius: 20rpx;
	box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
}

.message-item.user .message-content {
	background-color: #667eea;
	color: white;
}

.message-text {
	font-size: 28rpx;
	line-height: 1.5;
	display: block;
}

.message-time {
	font-size: 22rpx;
	color: #999;
	margin-top: 10rpx;
	display: block;
}

.message-item.user .message-time {
	color: rgba(255, 255, 255, 0.7);
}

.input-container {
	background-color: #fff;
	padding: 20rpx;
	border-top: 1rpx solid #eee;
}

.quick-actions {
	display: flex;
	gap: 15rpx;
	margin-bottom: 20rpx;
	flex-wrap: wrap;
}

.action-item {
	background-color: #f0f0f0;
	padding: 15rpx 25rpx;
	border-radius: 30rpx;
	border: 1rpx solid #ddd;
}

.action-text {
	font-size: 24rpx;
	color: #666;
}

.input-bar {
	display: flex;
	align-items: center;
	gap: 20rpx;
}

.message-input {
	flex: 1;
	height: 80rpx;
	padding: 0 30rpx;
	border: 1rpx solid #ddd;
	border-radius: 40rpx;
	font-size: 28rpx;
	background-color: #f8f8f8;
}

.send-btn {
	height: 80rpx;
	padding: 0 40rpx;
	background-color: #667eea;
	color: white;
	border: none;
	border-radius: 40rpx;
	font-size: 28rpx;
}

.send-btn[disabled] {
	background-color: #ccc;
}
</style>
