<template>
	<view class="profile-page">
		<text class="page-title">我的</text>
	</view>
	
	<!-- 自定义TabBar -->
	<custom-tabbar :current="4"></custom-tabbar>
</template>

<script>
import CustomTabbar from '../../components/custom-tabbar/custom-tabbar.vue'

export default {
	components: {
		CustomTabbar
	}
}
</script>

<style scoped>
.profile-page {
	display: flex;
	align-items: center;
	justify-content: center;
	min-height: 100vh;
	padding-bottom: 170rpx;
	background-color: #f5f5f5;
}

.page-title {
	font-size: 48rpx;
	font-weight: bold;
	color: #333;
}
</style>
