.tab-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: 198rpx;
  background-color: #ffffff;
  border-top: 1rpx solid #e5e5e5;
  display: flex;
  align-items: flex-end;
  justify-content: space-around;
  padding: 10rpx 0;
  z-index: 9999;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.1);
}

.tab-bar-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  position: relative;
}

/* 普通Tab样式 */
.normal-tab {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.tab-icon {
  width: 44rpx;
  height: 44rpx;
  margin-bottom: 6rpx;
}

.tab-text {
  font-size: 20rpx;
  line-height: 1.2;
}

/* AI灵愿中心凸起样式 */
.tab-bar-item:nth-child(3) {
  transform: translateY(-20rpx);
}

.ai-center-wrapper {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.ai-center-bg {
  width: 100rpx;
  height: 100rpx;
  border-radius: 50rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 8rpx;
  box-shadow: 0 4rpx 20rpx rgba(102, 126, 234, 0.4), 0 0 0 6rpx rgba(102, 126, 234, 0.2);
  border: 4rpx solid #ffffff;
  position: relative;
}

.ai-icon {
  width: 50rpx;
  height: 50rpx;
}

.ai-text {
  font-size: 18rpx;
  font-weight: bold;
  line-height: 1.2;
}