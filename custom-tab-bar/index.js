Component({
  data: {
    selected: 0,
    color: "#7A7E83",
    selectedColor: "#3cc51f",
    list: [
      {
        pagePath: "/pages/index/index",
        iconPath: "/static/tabbar/home.png",
        selectedIconPath: "/static/tabbar/home-active.png",
        text: "首页"
      },
      {
        pagePath: "/pages/explore/explore",
        iconPath: "/static/tabbar/explore.png",
        selectedIconPath: "/static/tabbar/explore-active.png",
        text: "探索"
      },
      {
        pagePath: "/pages/ai-center/ai-center",
        iconPath: "/static/tabbar/ai-center.png",
        selectedIconPath: "/static/tabbar/ai-center-active.png",
        text: "AI灵愿"
      },
      {
        pagePath: "/pages/rights/rights",
        iconPath: "/static/tabbar/rights.png",
        selectedIconPath: "/static/tabbar/rights-active.png",
        text: "权益"
      },
      {
        pagePath: "/pages/user/profile",
        iconPath: "/static/tabbar/profile.png",
        selectedIconPath: "/static/tabbar/profile-active.png",
        text: "我的"
      }
    ]
  },
  attached() {},
  methods: {
    switchTab(e) {
      const data = e.currentTarget.dataset
      const url = data.path
      wx.switchTab({url})
      this.setData({
        selected: data.index
      })
    }
  }
})