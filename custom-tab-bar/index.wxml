<view class="tab-bar">
  <view wx:for="{{list}}" wx:key="index" class="tab-bar-item" data-path="{{item.pagePath}}" data-index="{{index}}" bindtap="switchTab">
    <!-- AI灵愿中心特殊样式 -->
    <view wx:if="{{index === 2}}" class="ai-center-wrapper">
      <view class="ai-center-bg">
        <image src="{{selected === index ? item.selectedIconPath : item.iconPath}}" class="ai-icon"></image>
      </view>
      <view class="ai-text" style="color: {{selected === index ? '#FF6B6B' : '#667eea'}}">{{item.text}}</view>
    </view>
    
    <!-- 普通Tab样式 -->
    <view wx:else class="normal-tab">
      <image src="{{selected === index ? item.selectedIconPath : item.iconPath}}" class="tab-icon"></image>
      <view class="tab-text" style="color: {{selected === index ? selectedColor : color}}">{{item.text}}</view>
    </view>
  </view>
</view>