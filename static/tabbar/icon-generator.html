<!DOCTYPE html>
<html>
<head>
    <title>TabBar图标生成器</title>
    <style>
        .icon-canvas {
            border: 1px solid #ccc;
            margin: 10px;
        }
    </style>
</head>
<body>
    <h2>TabBar图标生成器</h2>
    <div>
        <canvas id="home" class="icon-canvas" width="32" height="32"></canvas>
        <canvas id="home-active" class="icon-canvas" width="32" height="32"></canvas>
        <canvas id="explore" class="icon-canvas" width="32" height="32"></canvas>
        <canvas id="explore-active" class="icon-canvas" width="32" height="32"></canvas>
        <canvas id="ai-center" class="icon-canvas" width="32" height="32"></canvas>
        <canvas id="ai-center-active" class="icon-canvas" width="32" height="32"></canvas>
        <canvas id="rights" class="icon-canvas" width="32" height="32"></canvas>
        <canvas id="rights-active" class="icon-canvas" width="32" height="32"></canvas>
        <canvas id="profile" class="icon-canvas" width="32" height="32"></canvas>
        <canvas id="profile-active" class="icon-canvas" width="32" height="32"></canvas>
    </div>
    
    <script>
        function createIcon(canvasId, text, color, bgColor = 'white') {
            const canvas = document.getElementById(canvasId);
            const ctx = canvas.getContext('2d');
            
            // 填充背景
            ctx.fillStyle = bgColor;
            ctx.fillRect(0, 0, 32, 32);
            
            // 绘制文字
            ctx.fillStyle = color;
            ctx.font = '12px Arial';
            ctx.textAlign = 'center';
            ctx.textBaseline = 'middle';
            ctx.fillText(text, 16, 16);
            
            // 下载图片
            const link = document.createElement('a');
            link.download = canvasId + '.png';
            link.href = canvas.toDataURL();
            link.click();
        }
        
        // 生成图标
        setTimeout(() => {
            createIcon('home', '🏠', '#666');
            createIcon('home-active', '🏠', '#3cc51f');
            createIcon('explore', '🔍', '#666');
            createIcon('explore-active', '🔍', '#3cc51f');
            createIcon('ai-center', '🤖', '#666');
            createIcon('ai-center-active', '🤖', '#3cc51f');
            createIcon('rights', '💎', '#666');
            createIcon('rights-active', '💎', '#3cc51f');
            createIcon('profile', '👤', '#666');
            createIcon('profile-active', '👤', '#3cc51f');
        }, 1000);
    </script>
</body>
</html>
