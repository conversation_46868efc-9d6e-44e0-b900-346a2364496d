<template>
	<view class="custom-tabbar">
		<!-- 普通Tab项 -->
		<view 
			class="tab-item" 
			v-for="(item, index) in tabList" 
			:key="index"
			@click="switchTab(item, index)"
			:class="{ active: currentIndex === index, 'ai-center': item.isAiCenter }"
		>
			<!-- AI灵愿特殊样式 -->
			<view v-if="item.isAiCenter" class="ai-center-wrapper">
				<view class="ai-center-bg">
					<image :src="currentIndex === index ? item.selectedIconPath : item.iconPath" class="ai-icon"></image>
				</view>
				<text class="ai-text">{{ item.text }}</text>
			</view>
			
			<!-- 普通Tab样式 -->
			<view v-else class="normal-tab">
				<image :src="currentIndex === index ? item.selectedIconPath : item.iconPath" class="tab-icon"></image>
				<text class="tab-text" :class="{ active: currentIndex === index }">{{ item.text }}</text>
			</view>
		</view>
	</view>
</template>

<script>
export default {
	name: 'CustomTabbar',
	props: {
		current: {
			type: Number,
			default: 0
		}
	},
	data() {
		return {
			currentIndex: 0,
			tabList: [
				{
					pagePath: '/pages/index/index',
					iconPath: '/static/tabbar/home.png',
					selectedIconPath: '/static/tabbar/home-active.png',
					text: '首页',
					isAiCenter: false
				},
				{
					pagePath: '/pages/explore/explore',
					iconPath: '/static/tabbar/explore.png',
					selectedIconPath: '/static/tabbar/explore-active.png',
					text: '探索',
					isAiCenter: false
				},
				{
					pagePath: '/pages/ai-center/ai-center',
					iconPath: '/static/tabbar/ai-center.png',
					selectedIconPath: '/static/tabbar/ai-center-active.png',
					text: 'AI百应',
					isAiCenter: true
				},
				{
					pagePath: '/pages/rights/rights',
					iconPath: '/static/tabbar/rights.png',
					selectedIconPath: '/static/tabbar/rights-active.png',
					text: '权益',
					isAiCenter: false
				},
				{
					pagePath: '/pages/user/profile',
					iconPath: '/static/tabbar/profile.png',
					selectedIconPath: '/static/tabbar/profile-active.png',
					text: '我的',
					isAiCenter: false
				}
			]
		}
	},
	watch: {
		current: {
			handler(newVal) {
				this.currentIndex = newVal;
			},
			immediate: true
		}
	},
	mounted() {
		// 初始化时获取当前页面索引
		this.updateCurrentIndex()
	},
	methods: {
		switchTab(item, index) {
			this.currentIndex = index;
			this.$emit('switch', index);
			
			// 切换页面
			uni.switchTab({
				url: item.pagePath,
				success: () => {
					console.log('切换Tab成功:', item.text);
				},
				fail: (err) => {
					console.error('切换Tab失败:', err);
				}
			});
		},
		
		updateCurrentIndex() {
			const pages = getCurrentPages();
			if (pages.length > 0) {
				const currentPage = pages[pages.length - 1];
				const route = currentPage.route;
				
				// 查找当前页面对应的tab索引
				const tabIndex = this.tabList.findIndex(item => {
					const pagePath = item.pagePath.replace('/', '');
					return route === pagePath;
				});
				
				if (tabIndex !== -1) {
					this.currentIndex = tabIndex;
				}
			}
		}
	}
}
</script>

<style scoped>
.custom-tabbar {
	position: fixed;
	bottom: 0;
	left: 0;
	right: 0;
	height: 130rpx;
	background-color: #ffffff;
	border-top: 1rpx solid #e5e5e5;
	display: flex;
	align-items: flex-end;
	justify-content: space-around;
	padding: 20rpx 0;
	z-index: 9999;
	box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.1);
}

.tab-item {
	flex: 1;
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	position: relative;
}

/* 普通Tab样式 */
.normal-tab {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
}

.tab-icon {
	width: 44rpx;
	height: 44rpx;
	margin-bottom: 6rpx;
}

.tab-text {
	font-size: 20rpx;
	color: #7a7e83;
	line-height: 1.2;
}

.tab-text.active {
	color: #3cc51f;
}

/* AI灵愿中心凸起样式 */
.ai-center {
	transform: translateY(-20rpx);
}

.ai-center-wrapper {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
}

.ai-center-bg {
	width: 100rpx;
	height: 100rpx;
	border-radius: 50rpx;
	background: linear-gradient(135deg, #26D0CE 0%, #1A9390 100%);
	display: flex;
	align-items: center;
	justify-content: center;
	margin-bottom: 8rpx;
	box-shadow: 0 4rpx 20rpx rgba(38, 208, 206, 0.4), 0 0 0 6rpx rgba(38, 208, 206, 0.2);
	border: 4rpx solid #ffffff;
	position: relative;
}

.ai-icon {
	width: 50rpx;
	height: 50rpx;
	filter: brightness(0) invert(1);
}

.ai-text {
	font-size: 18rpx;
	color: #1A9390;
	font-weight: bold;
	line-height: 1.2;
}

/* 激活状态下的AI中心 */
.ai-center.active .ai-center-bg {
	background: linear-gradient(135deg, #1DE9B6 0%, #00BFA5 100%);
	box-shadow: 0 6rpx 25rpx rgba(29, 233, 182, 0.5), 0 0 0 6rpx rgba(29, 233, 182, 0.2);
}

.ai-center.active .ai-text {
	color: #00BFA5;
}
</style>
