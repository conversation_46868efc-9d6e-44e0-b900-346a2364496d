<template>
	<view class="custom-tabbar">
		<!-- 普通Tab项 -->
		<view
			class="tab-item"
			v-for="(item, index) in tabList"
			:key="index"
			@click="switchTab(item, index)"
			:class="{ active: currentIndex === index, 'ai-center': item.isAiCenter }"
		>
			<!-- AI灵愿特殊样式 -->
			<view v-if="item.isAiCenter" class="ai-center-wrapper">
				<view class="ai-center-bg">
					<image :src="currentIndex === index ? item.selectedIconPath : item.iconPath" class="ai-icon"></image>
				</view>
			</view>

			<!-- 普通Tab样式 -->
			<view v-else class="normal-tab">
				<image :src="currentIndex === index ? item.selectedIconPath : item.iconPath" class="tab-icon"></image>
				<text class="tab-text" :class="{ active: currentIndex === index }">{{ item.text }}</text>
			</view>
		</view>
	</view>
</template>

<script>
export default {
	name: 'CustomTabbar',
	props: {
		current: {
			type: Number,
			default: 0
		}
	},
	data() {
		return {
			currentIndex: 0,
			tabList: [
				{
					pagePath: '/pages/index/index',
					iconPath: '/static/tabbar/home.png',
					selectedIconPath: '/static/tabbar/home-active.png',
					text: '首页',
					isAiCenter: false
				},
				{
					pagePath: '/pages/explore/explore',
					iconPath: '/static/tabbar/explore.png',
					selectedIconPath: '/static/tabbar/explore-active.png',
					text: '探索',
					isAiCenter: false
				},
				{
					pagePath: '/pages/ai-center/ai-center',
					iconPath: '/static/tabbar/ai-center.png',
					selectedIconPath: '/static/tabbar/ai-center-active.png',
					text: '导航',
					isAiCenter: true
				},
				{
					pagePath: '/pages/rights/rights',
					iconPath: '/static/tabbar/rights.png',
					selectedIconPath: '/static/tabbar/rights-active.png',
					text: '发现',
					isAiCenter: false
				},
				{
					pagePath: '/pages/user/profile',
					iconPath: '/static/tabbar/profile.png',
					selectedIconPath: '/static/tabbar/profile-active.png',
					text: '我的',
					isAiCenter: false
				}
			]
		}
	},
	watch: {
		current: {
			handler(newVal) {
				this.currentIndex = newVal;
			},
			immediate: true
		}
	},
	mounted() {
		// 初始化时获取当前页面索引
		this.updateCurrentIndex()
	},
	methods: {
		switchTab(item, index) {
			this.currentIndex = index;
			this.$emit('switch', index);
			
			// 切换页面
			uni.switchTab({
				url: item.pagePath,
				success: () => {
					console.log('切换Tab成功:', item.text);
				},
				fail: (err) => {
					console.error('切换Tab失败:', err);
				}
			});
		},
		
		updateCurrentIndex() {
			const pages = getCurrentPages();
			if (pages.length > 0) {
				const currentPage = pages[pages.length - 1];
				const route = currentPage.route;
				
				// 查找当前页面对应的tab索引
				const tabIndex = this.tabList.findIndex(item => {
					const pagePath = item.pagePath.replace('/', '');
					return route === pagePath;
				});
				
				if (tabIndex !== -1) {
					this.currentIndex = tabIndex;
				}
			}
		}
	}
}
</script>

<style scoped>
.custom-tabbar {
	position: fixed;
	bottom: 0;
	left: 0;
	right: 0;
	height: 120rpx;
	background-color: #ffffff;
	border-top: 1rpx solid #f0f0f0;
	display: flex;
	align-items: center;
	justify-content: space-around;
	padding: 0;
	z-index: 9999;
	box-shadow: 0 -1rpx 6rpx rgba(0, 0, 0, 0.05);
}

.tab-item {
	flex: 1;
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	position: relative;
	height: 120rpx;
}

/* 普通Tab样式 */
.normal-tab {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	padding: 12rpx 0;
}

.tab-icon {
	width: 48rpx;
	height: 48rpx;
	margin-bottom: 8rpx;
}

.tab-text {
	font-size: 22rpx;
	color: #999999;
	line-height: 1.2;
	font-weight: 400;
}

.tab-text.active {
	color: #FF6B35;
	font-weight: 500;
}

/* AI中心凸起样式 */
.ai-center {
	transform: translateY(-30rpx);
}

.ai-center-wrapper {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	position: relative;
}

.ai-center-bg {
	width: 120rpx;
	height: 120rpx;
	border-radius: 60rpx;
	background: linear-gradient(135deg, #4CAF50 0%, #2E7D32 100%);
	display: flex;
	align-items: center;
	justify-content: center;
	box-shadow: 0 8rpx 24rpx rgba(76, 175, 80, 0.3);
	border: 6rpx solid #ffffff;
	position: relative;
}

.ai-center-bg::before {
	content: '';
	position: absolute;
	top: -3rpx;
	left: -3rpx;
	right: -3rpx;
	bottom: -3rpx;
	border-radius: 66rpx;
	background: linear-gradient(135deg, #4CAF50, #2E7D32);
	z-index: -1;
}

.ai-icon {
	width: 56rpx;
	height: 56rpx;
	filter: brightness(0) invert(1);
}

/* 激活状态下的AI中心 */
.ai-center.active .ai-center-bg {
	background: linear-gradient(135deg, #66BB6A 0%, #388E3C 100%);
	box-shadow: 0 10rpx 30rpx rgba(102, 187, 106, 0.4);
	transform: scale(1.05);
	transition: all 0.2s ease;
}
</style>
